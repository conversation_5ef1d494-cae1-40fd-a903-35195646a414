# 静态构建版本的Dockerfile - 更轻量级
FROM node:18-alpine AS builder

WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 设置环境变量
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV NEXT_PUBLIC_API_URL=http://47.79.39.147:8000

# 构建应用
RUN npm run build

# 生产环境 - 使用nginx提供静态文件
FROM nginx:alpine

# 复制nginx配置
COPY nginx.frontend.conf /etc/nginx/conf.d/default.conf

# 复制构建的文件
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static
COPY --from=builder /app/public ./public

# 暴露端口
EXPOSE 80

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
